import { prisma } from '@/lib/prisma';
import { 
  ConversionRequest, 
  ConversionResponse, 
  WarrantyToAmcConversion,
  AmcToOutWarrantyConversion,
  WarrantyToOutWarrantyConversion
} from '@/lib/validations/conversion.schema';
import { getAMCContractRepository, getWarrantyRepository } from '@/lib/repositories';

/**
 * Conversion Service
 * 
 * Handles module conversions between Warranty, AMC, and Out-of-Warranty records
 */
export class ConversionService {
  
  /**
   * Process a conversion request
   */
  static async processConversion(request: ConversionRequest): Promise<ConversionResponse> {
    return await prisma.$transaction(async (tx) => {
      switch (request.conversionType) {
        case 'WARRANTY_TO_AMC':
          return await this.convertWarrantyToAmc(request as WarrantyToAmcConversion, tx);
        case 'AMC_TO_OUT_WARRANTY':
          return await this.convertAmcToOutWarranty(request as AmcToOutWarrantyConversion, tx);
        case 'WARRANTY_TO_OUT_WARRANTY':
          return await this.convertWarrantyToOutWarranty(request as WarrantyToOutWarrantyConversion, tx);
        default:
          throw new Error(`Unsupported conversion type: ${request.conversionType}`);
      }
    });
  }

  /**
   * Convert Warranty to AMC
   */
  private static async convertWarrantyToAmc(
    request: WarrantyToAmcConversion, 
    tx: any
  ): Promise<ConversionResponse> {
    // Get source warranty
    const warranty = await tx.warranties.findUnique({
      where: { id: request.sourceId },
      include: {
        customer: true,
        machines: true,
        components: true,
      }
    });

    if (!warranty) {
      throw new Error('Source warranty not found');
    }

    // Check if warranty is already converted
    const existingConversion = await tx.history_cards.findFirst({
      where: {
        inWarrantyId: request.sourceId,
        source: 'WARRANTY_TO_AMC'
      }
    });

    if (existingConversion) {
      throw new Error('Warranty has already been converted to AMC');
    }

    // Create AMC contract
    const amcContract = await tx.amc_contracts.create({
      data: {
        customerId: warranty.customerId,
        executiveId: request.amcData.executiveId || warranty.executiveId,
        contactPersonId: request.amcData.contactPersonId || warranty.contactPersonId,
        natureOfService: request.amcData.natureOfService,
        startDate: request.amcData.startDate,
        endDate: request.amcData.endDate,
        amount: request.amcData.amount,
        numberOfServices: request.amcData.numberOfServices,
        numberOfMachines: warranty.numberOfMachines,
        status: 'ACTIVE',
        remarks: `Converted from warranty ${warranty.id}. ${request.notes || ''}`.trim(),
        inWarrantySourceId: warranty.id,
      }
    });

    // Copy machines to AMC
    if (warranty.machines && warranty.machines.length > 0) {
      const amcMachines = warranty.machines.map(machine => ({
        amcContractId: amcContract.id,
        productId: machine.productId,
        modelId: machine.modelId,
        brandId: machine.brandId,
        serialNumber: machine.serialNumber,
        location: machine.location,
        tonnage: machine.tonnage,
        status: 'ACTIVE',
        originalWarrantyMachineId: machine.id,
      }));

      await tx.amc_machines.createMany({
        data: amcMachines
      });
    }

    // Copy components to AMC
    if (warranty.components && warranty.components.length > 0) {
      // Get the created AMC machines to link components
      const createdAmcMachines = await tx.amc_machines.findMany({
        where: { amcContractId: amcContract.id }
      });

      const amcComponents = warranty.components.map(component => {
        const correspondingMachine = createdAmcMachines.find(
          amcMachine => amcMachine.originalWarrantyMachineId === component.warrantyMachineId
        );

        return {
          amcContractId: amcContract.id,
          amcMachineId: correspondingMachine?.id,
          componentType: component.componentType,
          serialNumber: component.serialNumber,
          originalWarrantyComponentId: component.id,
        };
      });

      await tx.amc_components.createMany({
        data: amcComponents
      });
    }

    // Create history card
    const historyCard = await tx.history_cards.create({
      data: {
        customerId: warranty.customerId,
        source: 'WARRANTY_TO_AMC',
        inWarrantyId: warranty.id,
        amcId: amcContract.id,
        cardNo: await this.getNextCardNumber(tx),
      }
    });

    // Update warranty status to converted
    await tx.warranties.update({
      where: { id: warranty.id },
      data: { 
        status: 'CONVERTED',
        remarks: `Converted to AMC ${amcContract.id} on ${request.effectiveDate.toISOString().split('T')[0]}. ${warranty.remarks || ''}`.trim()
      }
    });

    return {
      success: true,
      message: 'Warranty successfully converted to AMC',
      sourceId: warranty.id,
      targetId: amcContract.id,
      historyCardId: historyCard.id,
      conversionType: 'WARRANTY_TO_AMC',
      effectiveDate: request.effectiveDate,
    };
  }

  /**
   * Convert AMC to Out-of-Warranty
   */
  private static async convertAmcToOutWarranty(
    request: AmcToOutWarrantyConversion,
    tx: any
  ): Promise<ConversionResponse> {
    // Get source AMC contract
    const amcContract = await tx.amc_contracts.findUnique({
      where: { id: request.sourceId },
      include: {
        customer: true,
        machines: true,
        components: true,
      }
    });

    if (!amcContract) {
      throw new Error('Source AMC contract not found');
    }

    // Check if AMC is already converted
    const existingConversion = await tx.history_cards.findFirst({
      where: {
        amcId: request.sourceId,
        source: 'AMC_TO_OUT_WARRANTY'
      }
    });

    if (existingConversion) {
      throw new Error('AMC contract has already been converted to out-of-warranty');
    }

    // Create out-of-warranty record
    const outWarranty = await tx.out_warranties.create({
      data: {
        customerId: amcContract.customerId,
        executiveId: request.outWarrantyData.executiveId || amcContract.executiveId,
        contactPersonId: request.outWarrantyData.contactPersonId || amcContract.contactPersonId,
        startDate: request.outWarrantyData.startDate,
        endDate: request.outWarrantyData.endDate,
        technicianId: request.outWarrantyData.technicianId,
        numberOfMachines: amcContract.numberOfMachines,
        status: 'ACTIVE',
        remarks: `Converted from AMC ${amcContract.id}. ${request.notes || ''}`.trim(),
        amcSourceId: amcContract.id,
      }
    });

    // Copy machines to out-warranty
    if (amcContract.machines && amcContract.machines.length > 0) {
      const outWarrantyMachines = amcContract.machines.map(machine => ({
        outWarrantyId: outWarranty.id,
        productId: machine.productId,
        modelId: machine.modelId,
        brandId: machine.brandId,
        serialNumber: machine.serialNumber,
        location: machine.location,
        tonnage: machine.tonnage,
        status: 'ACTIVE',
        originalAmcMachineId: machine.id,
      }));

      await tx.out_warranty_machines.createMany({
        data: outWarrantyMachines
      });
    }

    // Copy components to out-warranty
    if (amcContract.components && amcContract.components.length > 0) {
      const createdOutWarrantyMachines = await tx.out_warranty_machines.findMany({
        where: { outWarrantyId: outWarranty.id }
      });

      const outWarrantyComponents = amcContract.components.map(component => {
        const correspondingMachine = createdOutWarrantyMachines.find(
          outMachine => outMachine.originalAmcMachineId === component.amcMachineId
        );

        return {
          outWarrantyId: outWarranty.id,
          outWarrantyMachineId: correspondingMachine?.id,
          componentType: component.componentType,
          serialNumber: component.serialNumber,
          originalAmcComponentId: component.id,
        };
      });

      await tx.out_warranty_components.createMany({
        data: outWarrantyComponents
      });
    }

    // Create history card
    const historyCard = await tx.history_cards.create({
      data: {
        customerId: amcContract.customerId,
        source: 'AMC_TO_OUT_WARRANTY',
        amcId: amcContract.id,
        outWarrantyId: outWarranty.id,
        cardNo: await this.getNextCardNumber(tx),
      }
    });

    // Update AMC status to converted
    await tx.amc_contracts.update({
      where: { id: amcContract.id },
      data: { 
        status: 'CONVERTED',
        remarks: `Converted to out-warranty ${outWarranty.id} on ${request.effectiveDate.toISOString().split('T')[0]}. ${amcContract.remarks || ''}`.trim()
      }
    });

    return {
      success: true,
      message: 'AMC successfully converted to out-of-warranty',
      sourceId: amcContract.id,
      targetId: outWarranty.id,
      historyCardId: historyCard.id,
      conversionType: 'AMC_TO_OUT_WARRANTY',
      effectiveDate: request.effectiveDate,
    };
  }

  /**
   * Convert Warranty to Out-of-Warranty
   */
  private static async convertWarrantyToOutWarranty(
    request: WarrantyToOutWarrantyConversion,
    tx: any
  ): Promise<ConversionResponse> {
    // Get source warranty
    const warranty = await tx.warranties.findUnique({
      where: { id: request.sourceId },
      include: {
        customer: true,
        machines: true,
        components: true,
      }
    });

    if (!warranty) {
      throw new Error('Source warranty not found');
    }

    // Check if warranty is already converted
    const existingConversion = await tx.history_cards.findFirst({
      where: {
        inWarrantyId: request.sourceId,
        source: 'WARRANTY_TO_OUT_WARRANTY'
      }
    });

    if (existingConversion) {
      throw new Error('Warranty has already been converted to out-of-warranty');
    }

    // Create out-of-warranty record
    const outWarranty = await tx.out_warranties.create({
      data: {
        customerId: warranty.customerId,
        executiveId: request.outWarrantyData.executiveId || warranty.executiveId,
        contactPersonId: request.outWarrantyData.contactPersonId || warranty.contactPersonId,
        startDate: request.outWarrantyData.startDate,
        endDate: request.outWarrantyData.endDate,
        technicianId: request.outWarrantyData.technicianId,
        numberOfMachines: warranty.numberOfMachines,
        status: 'ACTIVE',
        remarks: `Converted from warranty ${warranty.id}. ${request.notes || ''}`.trim(),
        inWarrantySourceId: warranty.id,
      }
    });

    // Copy machines to out-warranty
    if (warranty.machines && warranty.machines.length > 0) {
      const outWarrantyMachines = warranty.machines.map(machine => ({
        outWarrantyId: outWarranty.id,
        productId: machine.productId,
        modelId: machine.modelId,
        brandId: machine.brandId,
        serialNumber: machine.serialNumber,
        location: machine.location,
        tonnage: machine.tonnage,
        status: 'ACTIVE',
        originalWarrantyMachineId: machine.id,
      }));

      await tx.out_warranty_machines.createMany({
        data: outWarrantyMachines
      });
    }

    // Copy components to out-warranty
    if (warranty.components && warranty.components.length > 0) {
      const createdOutWarrantyMachines = await tx.out_warranty_machines.findMany({
        where: { outWarrantyId: outWarranty.id }
      });

      const outWarrantyComponents = warranty.components.map(component => {
        const correspondingMachine = createdOutWarrantyMachines.find(
          outMachine => outMachine.originalWarrantyMachineId === component.warrantyMachineId
        );

        return {
          outWarrantyId: outWarranty.id,
          outWarrantyMachineId: correspondingMachine?.id,
          componentType: component.componentType,
          serialNumber: component.serialNumber,
          originalWarrantyComponentId: component.id,
        };
      });

      await tx.out_warranty_components.createMany({
        data: outWarrantyComponents
      });
    }

    // Create history card
    const historyCard = await tx.history_cards.create({
      data: {
        customerId: warranty.customerId,
        source: 'WARRANTY_TO_OUT_WARRANTY',
        inWarrantyId: warranty.id,
        outWarrantyId: outWarranty.id,
        cardNo: await this.getNextCardNumber(tx),
      }
    });

    // Update warranty status to converted
    await tx.warranties.update({
      where: { id: warranty.id },
      data: {
        status: 'CONVERTED',
        remarks: `Converted to out-warranty ${outWarranty.id} on ${request.effectiveDate.toISOString().split('T')[0]}. ${warranty.remarks || ''}`.trim()
      }
    });

    return {
      success: true,
      message: 'Warranty successfully converted to out-of-warranty',
      sourceId: warranty.id,
      targetId: outWarranty.id,
      historyCardId: historyCard.id,
      conversionType: 'WARRANTY_TO_OUT_WARRANTY',
      effectiveDate: request.effectiveDate,
    };
  }

  /**
   * Get next card number for history cards
   */
  private static async getNextCardNumber(tx: any): Promise<number> {
    const lastCard = await tx.history_cards.findFirst({
      orderBy: { cardNo: 'desc' },
      select: { cardNo: true }
    });

    return (lastCard?.cardNo || 0) + 1;
  }

  /**
   * Validate conversion eligibility
   */
  static async validateConversion(sourceId: string, conversionType: string): Promise<{ valid: boolean; message: string }> {
    try {
      switch (conversionType) {
        case 'WARRANTY_TO_AMC':
          const warranty = await prisma.warranties.findUnique({
            where: { id: sourceId },
            select: { status: true }
          });
          
          if (!warranty) {
            return { valid: false, message: 'Warranty not found' };
          }
          
          if (warranty.status === 'CONVERTED') {
            return { valid: false, message: 'Warranty has already been converted' };
          }
          
          return { valid: true, message: 'Warranty is eligible for conversion' };

        case 'AMC_TO_OUT_WARRANTY':
          const amc = await prisma.amc_contracts.findUnique({
            where: { id: sourceId },
            select: { status: true }
          });

          if (!amc) {
            return { valid: false, message: 'AMC contract not found' };
          }

          if (amc.status === 'CONVERTED') {
            return { valid: false, message: 'AMC contract has already been converted' };
          }

          return { valid: true, message: 'AMC contract is eligible for conversion' };

        case 'WARRANTY_TO_OUT_WARRANTY':
          const warrantyForOut = await prisma.warranties.findUnique({
            where: { id: sourceId },
            select: { status: true }
          });

          if (!warrantyForOut) {
            return { valid: false, message: 'Warranty not found' };
          }

          if (warrantyForOut.status === 'CONVERTED') {
            return { valid: false, message: 'Warranty has already been converted' };
          }

          return { valid: true, message: 'Warranty is eligible for conversion to out-of-warranty' };

        default:
          return { valid: false, message: 'Unsupported conversion type' };
      }
    } catch (error) {
      return { valid: false, message: 'Error validating conversion eligibility' };
    }
  }
}
