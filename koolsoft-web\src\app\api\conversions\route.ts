import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ConversionService } from '@/lib/services/conversion.service';
import { 
  conversionRequestSchema, 
  bulkConversionSchema,
  conversionHistoryFilterSchema 
} from '@/lib/validations/conversion.schema';
import { prisma } from '@/lib/prisma';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { z } from 'zod';

/**
 * GET /api/conversions
 * Get conversion history with filtering and pagination
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const filterParams = {
        customerId: searchParams.get('customerId') || undefined,
        conversionType: searchParams.get('conversionType') || undefined,
        dateFrom: searchParams.get('dateFrom') ? new Date(searchParams.get('dateFrom')!) : undefined,
        dateTo: searchParams.get('dateTo') ? new Date(searchParams.get('dateTo')!) : undefined,
        skip: parseInt(searchParams.get('skip') || '0'),
        take: parseInt(searchParams.get('take') || '10'),
      };

      const validatedFilters = conversionHistoryFilterSchema.parse(filterParams);

      // Build where clause
      const where: any = {};
      
      if (validatedFilters.customerId) {
        where.customerId = validatedFilters.customerId;
      }
      
      if (validatedFilters.conversionType) {
        where.source = validatedFilters.conversionType;
      }
      
      if (validatedFilters.dateFrom || validatedFilters.dateTo) {
        where.createdAt = {};
        if (validatedFilters.dateFrom) {
          where.createdAt.gte = validatedFilters.dateFrom;
        }
        if (validatedFilters.dateTo) {
          where.createdAt.lte = validatedFilters.dateTo;
        }
      }

      // Get conversion history
      const [conversions, total] = await Promise.all([
        prisma.history_cards.findMany({
          where,
          include: {
            customer: {
              select: {
                id: true,
                name: true,
              }
            },
            amcContract: {
              select: {
                id: true,
                amount: true,
                startDate: true,
                endDate: true,
                status: true,
              }
            },
            inWarranty: {
              select: {
                id: true,
                bslNo: true,
                installDate: true,
                warrantyDate: true,
              }
            },
            outWarranty: {
              select: {
                id: true,
                startDate: true,
                endDate: true,
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip: validatedFilters.skip,
          take: validatedFilters.take,
        }),
        prisma.history_cards.count({ where })
      ]);

      return NextResponse.json({
        data: conversions,
        pagination: {
          total,
          skip: validatedFilters.skip,
          take: validatedFilters.take,
          hasMore: validatedFilters.skip + validatedFilters.take < total,
        }
      });

    } catch (error) {
      console.error('Error fetching conversion history:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid filter parameters', details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to fetch conversion history' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/conversions
 * Process a single conversion request
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      const validatedData = conversionRequestSchema.parse(body);

      // Validate conversion eligibility
      const eligibility = await ConversionService.validateConversion(
        validatedData.sourceId,
        validatedData.conversionType
      );

      if (!eligibility.valid) {
        return NextResponse.json(
          { error: eligibility.message },
          { status: 400 }
        );
      }

      // Process the conversion
      const result = await ConversionService.processConversion(validatedData);

      return NextResponse.json(result, { status: 201 });

    } catch (error) {
      console.error('Error processing conversion:', error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Validation error', details: error.errors },
          { status: 400 }
        );
      }

      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          return NextResponse.json(
            { error: 'Conversion already exists or duplicate constraint violation' },
            { status: 409 }
          );
        }
        if (error.code === 'P2025') {
          return NextResponse.json(
            { error: 'Source record not found' },
            { status: 404 }
          );
        }
      }

      if (error instanceof Error) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to process conversion' },
        { status: 500 }
      );
    }
  }
);

/**
 * PUT /api/conversions
 * Process bulk conversions
 */
export const PUT = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      const validatedData = bulkConversionSchema.parse(body);

      const results = [];
      const errors = [];

      // If validation only, don't process conversions
      if (validatedData.validateOnly) {
        for (const conversion of validatedData.conversions) {
          try {
            const eligibility = await ConversionService.validateConversion(
              conversion.sourceId,
              conversion.conversionType
            );
            
            results.push({
              sourceId: conversion.sourceId,
              conversionType: conversion.conversionType,
              valid: eligibility.valid,
              message: eligibility.message,
            });
          } catch (error) {
            errors.push({
              sourceId: conversion.sourceId,
              conversionType: conversion.conversionType,
              error: error instanceof Error ? error.message : 'Unknown error',
            });
          }
        }

        return NextResponse.json({
          validationResults: results,
          errors,
          totalRequested: validatedData.conversions.length,
          validCount: results.filter(r => r.valid).length,
          invalidCount: results.filter(r => !r.valid).length,
          errorCount: errors.length,
        });
      }

      // Process conversions
      for (const conversion of validatedData.conversions) {
        try {
          // Validate eligibility first
          const eligibility = await ConversionService.validateConversion(
            conversion.sourceId,
            conversion.conversionType
          );

          if (!eligibility.valid) {
            errors.push({
              sourceId: conversion.sourceId,
              conversionType: conversion.conversionType,
              error: eligibility.message,
            });
            continue;
          }

          // Process the conversion
          const result = await ConversionService.processConversion(conversion);
          results.push(result);

        } catch (error) {
          console.error(`Error processing conversion for ${conversion.sourceId}:`, error);
          errors.push({
            sourceId: conversion.sourceId,
            conversionType: conversion.conversionType,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      return NextResponse.json({
        results,
        errors,
        totalRequested: validatedData.conversions.length,
        successCount: results.length,
        errorCount: errors.length,
      }, { status: 201 });

    } catch (error) {
      console.error('Error processing bulk conversions:', error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Validation error', details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to process bulk conversions' },
        { status: 500 }
      );
    }
  }
);
