import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ConversionService } from '@/lib/services/conversion.service';
import { z } from 'zod';

// Define conversion type schema locally to avoid import issues
const conversionTypeSchema = z.enum([
  'WARRANTY_TO_AMC',
  'AMC_TO_OUT_WARRANTY',
  'WARRANTY_TO_OUT_WARRANTY'
], {
  errorMap: () => ({ message: 'Invalid conversion type' })
});


/**
 * Validation request schema
 */
const validationRequestSchema = z.object({
  sourceId: z.string().uuid({ message: 'Valid source ID is required' }),
  conversionType: conversionTypeSchema,
});

/**
 * POST /api/conversions/validate
 * Validate if a conversion is possible
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      const validatedData = validationRequestSchema.parse(body);

      // Validate conversion eligibility
      const result = await ConversionService.validateConversion(
        validatedData.sourceId,
        validatedData.conversionType
      );

      return NextResponse.json({
        sourceId: validatedData.sourceId,
        conversionType: validatedData.conversionType,
        valid: result.valid,
        message: result.message,
      });

    } catch (error) {
      console.error('Error validating conversion:', error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Validation error', details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to validate conversion' },
        { status: 500 }
      );
    }
  }
);

/**
 * GET /api/conversions/validate
 * Validate conversion with query parameters
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      const sourceId = searchParams.get('sourceId');
      const conversionType = searchParams.get('conversionType');

      if (!sourceId || !conversionType) {
        return NextResponse.json(
          { error: 'sourceId and conversionType are required' },
          { status: 400 }
        );
      }

      // Validate parameters
      const validatedData = validationRequestSchema.parse({
        sourceId,
        conversionType,
      });

      // Validate conversion eligibility
      const result = await ConversionService.validateConversion(
        validatedData.sourceId,
        validatedData.conversionType
      );

      return NextResponse.json({
        sourceId: validatedData.sourceId,
        conversionType: validatedData.conversionType,
        valid: result.valid,
        message: result.message,
      });

    } catch (error) {
      console.error('Error validating conversion:', error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Validation error', details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to validate conversion' },
        { status: 500 }
      );
    }
  }
);
